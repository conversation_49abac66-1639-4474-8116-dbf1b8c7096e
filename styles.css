/* Reset e configurações básicas */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Comic Neue', cursive;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header e Navegação */
.navbar {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 1rem 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    color: #FF6B6B;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    transition: color 0.3s ease;
    position: relative;
}

.nav-menu a:hover {
    color: #FF6B6B;
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: #FF6B6B;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after {
    width: 100%;
}

/* Seção Hero */
.hero {
    margin-top: 80px;
    padding: 4rem 0;
    display: flex;
    align-items: center;
    min-height: 70vh;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
    padding-left: 20px;
    padding-right: 20px;
}

.hero-content {
    flex: 1;
    padding-right: 2rem;
}

.hero-content h2 {
    font-size: 3rem;
    color: #FF6B6B;
    margin-bottom: 1rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    color: #555;
}

.cta-button {
    background: linear-gradient(45deg, #FF6B6B, #FFD93D);
    color: white;
    border: none;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
}

.hero-image {
    flex: 1;
    text-align: center;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Seções */
section {
    padding: 4rem 0;
}

h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #FF6B6B;
    margin-bottom: 3rem;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

/* Atividades */
.activities {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.activity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.activity-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border: 3px solid transparent;
    background-clip: padding-box;
}

.activity-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    border-color: #FFD93D;
}

.activity-card h3 {
    color: #FF6B6B;
    margin-bottom: 1rem;
    font-size: 1.3rem;
}

.activity-btn {
    background: linear-gradient(45deg, #4ECDC4, #44A08D);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: transform 0.3s ease;
    margin-top: 1rem;
}

.activity-btn:hover {
    transform: scale(1.05);
}

/* Galeria de Desenhos */
.coloring-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 2rem;
}

.coloring-item {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.coloring-item:hover {
    transform: scale(1.05);
}

.coloring-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.coloring-item h4 {
    padding: 1rem;
    color: #FF6B6B;
    text-align: center;
}

/* Sobre */
.about {
    background: rgba(255, 255, 255, 0.9);
    text-align: center;
}

.about p {
    font-size: 1.1rem;
    max-width: 800px;
    margin: 0 auto;
    color: #555;
}

/* Footer */
footer {
    background: #333;
    color: white;
    text-align: center;
    padding: 2rem 0;
}

/* Responsividade */
@media (max-width: 768px) {
    .hero {
        flex-direction: column;
        text-align: center;
    }
    
    .hero-content {
        padding-right: 0;
        margin-bottom: 2rem;
    }
    
    .hero-content h2 {
        font-size: 2rem;
    }
    
    .nav-menu {
        gap: 1rem;
    }
    
    .activity-grid {
        grid-template-columns: 1fr;
    }
}
